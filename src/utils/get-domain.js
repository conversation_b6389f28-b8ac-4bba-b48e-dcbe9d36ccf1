import punycode from 'punycode/';

export default function getDomain(url) {
  try {
    return punycode.toUnicode(
      URL.parse(url)
        .hostname.replace(/^www\./, '')
        .replace(/\/$/, ''),
    );
  } catch (e) {
    return ''; // just give up
  }
}

// Parse account instance domain from account URL
// Used for displaying role tags with correct instance domain
export function parseAccountInstance(url) {
  if (!url) return null;
  const hostname = URL.parse(url)?.hostname;
  if (!hostname) return null;
  const domain = punycode.toUnicode(hostname);
  return domain;
}
